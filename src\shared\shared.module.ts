import type { Provider } from '@nestjs/common';
import { Global, Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { ApiConfigService } from './services/api-config.service.ts';
import { GeneratorService } from './services/generator.service.ts';
import { TranslationService } from './services/translation.service.ts';
import { ValidatorService } from './services/validator.service.ts';
import { CacheService } from './services/cache.service.ts';
import { RedisService } from './services/redis.service.ts';

const providers: Provider[] = [
	ApiConfigService,
	ValidatorService,
	GeneratorService,
	TranslationService,
	CacheService,
	RedisService,
];

@Global()
@Module({
	providers,
	imports: [CqrsModule],
	exports: [...providers, CqrsModule],
})
export class SharedModule {}
