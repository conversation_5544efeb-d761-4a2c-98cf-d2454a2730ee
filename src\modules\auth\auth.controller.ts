import { getIp } from '@common/utils.ts';
import { RoleType } from '@constants/role-type';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import { UserAccountDto } from '@modules/user/dtos/user-account.dto';
import { UserService } from '@modules/user/user.service';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import {
	Body,
	Controller,
	Get,
	HttpCode,
	HttpStatus,
	Post,
	Req,
	Res,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import type { Request, Response } from 'express';

import { AuthService } from './auth.service.ts';
import { LoginPayloadDto } from './dto/login-payload.dto.ts';
import { RefreshTokenDto } from './dto/refresh-token.dto.ts';
import type { SocialInfoDto } from './dto/social-info.dto.ts';
import { TokenPayloadDto } from './dto/token-payload.dto.ts';
import { UserLoginDto } from './dto/user-login.dto.ts';
import { UserRegisterDto } from './dto/user-register.dto.ts';
import type { ApiConfigService } from '@shared/services/api-config.service.ts';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
	constructor(
		private userService: UserService,
		private authService: AuthService,
		private configService: ApiConfigService,
	) {}

	@Post('login')
	@HttpCode(HttpStatus.OK)
	@ApiOkResponse({
		type: LoginPayloadDto,
		description: 'User info with access token',
	})
	async userLogin(
		@Body() userLoginDto: UserLoginDto,
		@Req() request: Request,
	): Promise<LoginPayloadDto> {
		const userAccountEntity = await this.authService.validateUser(userLoginDto);

		const token = await this.authService.createToken({
			userId: userAccountEntity.userId,
			role: RoleType.USER,
		});
		console.info(
			`User ${userAccountEntity.username} logged in with access token: ${token.token}`,
		);

		this.userService.updateLastLoginInfo(
			userAccountEntity.userId,
			getIp(request),
		);
		this.userService.updateRefreshToken(
			userAccountEntity.userId,
			token.refreshToken,
		);

		return new LoginPayloadDto(userAccountEntity.toDto(), token);
	}

	@Post('register')
	@HttpCode(HttpStatus.OK)
	@ApiOkResponse({
		type: UserAccountDto,
		description: 'Successfully Registered',
	})
	async userRegister(
		@Body() userRegisterDto: UserRegisterDto,
		@Req() request: Request,
	): Promise<UserAccountDto> {
		if (!userRegisterDto.username) {
			throw new Error('Username must be provided');
		}

		const createdUser = await this.userService.createUser(
			userRegisterDto,
			getIp(request),
		);

		return createdUser.toDto();
	}

	@Post('refresh-token')
	@HttpCode(HttpStatus.OK)
	@ApiOkResponse({ type: TokenPayloadDto, description: 'Get new access token' })
	async userRefreshToken(
		@Body() refreshTokenDto: RefreshTokenDto,
	): Promise<TokenPayloadDto> {
		if (!refreshTokenDto.refreshToken) {
			throw new Error('Refresh token must be provided');
		}

		const userAccountEntity = await this.authService.getUserFromRefreshToken(
			refreshTokenDto.refreshToken,
		);

		if (!userAccountEntity) {
			throw new Error('Invalid refresh token');
		}

		const token = await this.authService.createToken({
			userId: userAccountEntity.userId,
			role: RoleType.USER,
		});

		this.userService.updateRefreshToken(
			userAccountEntity.userId,
			token.refreshToken,
		);

		return token;
	}

	@Post('logout')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({
		type: UserAccountDto,
		description: 'Successfully logged out',
	})
	async userLogout(
		@AuthUser() user: UserAccountEntity,
	): Promise<{ message: string }> {
		await this.userService.updateRefreshToken(user.userId, null);
		console.info(`User ${user.username} logged out`);

		return { message: 'Successfully logged out' };
	}

	@Get('oauth2/google')
	@UseGuards(AuthGuard('google'))
	async googleLogin() {
		console.info('Redirecting to Google for OAuth2 login');
	}

	@Get('oauth2/google/callback')
	@UseGuards(AuthGuard('google'))
	async googleCallback(@Req() req: Request, @Res() res: Response) {
		var socialInfo = req.user as SocialInfoDto;
		const response = await this.authService.loginWithOAuth(
			socialInfo,
			getIp(req),
		);

		if (!response.ssoToken) {
			res.redirect(
				`${this.configService.frontendUrl}/auth/callback?status=${response.status}`,
			);
			return;
		}

		res.redirect(
			`${this.configService.frontendUrl}/auth/callback?token=${response.ssoToken}`,
		);
	}

	@Get('oauth2/facebook')
	@UseGuards(AuthGuard('facebook'))
	async facebookLogin() {
		console.info('Redirecting to Facebook for OAuth2 login');
	}

	@Get('oauth2/facebook/callback')
	@UseGuards(AuthGuard('facebook'))
	async facebookCallback(@Req() req: Request, @Res() res: Response) {
		var socialInfo = req.user as SocialInfoDto;
		const response = await this.authService.loginWithOAuth(
			socialInfo,
			getIp(req),
		);

		if (!response.ssoToken) {
			res.redirect(
				`${this.configService.frontendUrl}/auth/callback?status=${response.status}`,
			);
			return;
		}

		res.redirect(
			`${this.configService.frontendUrl}/auth/callback?token=${response.ssoToken}`,
		);
	}
}
