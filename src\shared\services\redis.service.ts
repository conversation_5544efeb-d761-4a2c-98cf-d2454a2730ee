import { Injectable } from '@nestjs/common';
import type { OnModuleDestroy } from '@nestjs/common';
import { createClient } from 'redis';
import type { RedisClientType } from 'redis';
import { ApiConfigService } from './api-config.service';

@Injectable()
export class RedisService implements OnModuleDestroy {
	private client!: RedisClientType;
	private readonly keyPrefix = 'fsp::';

	constructor(private readonly configService: ApiConfigService) {
		this.initializeClient();
	}

	private async initializeClient(): Promise<void> {
		this.client = createClient({
			socket: this.configService.redisConfig.socket,
		});

		this.client.on('error', (err) => {
			console.error('Redis Client Error:', err);
		});

		this.client.on('connect', () => {
			console.log('Redis Client Connected');
		});

		try {
			await this.client.connect();
			console.log('Redis connection established successfully');
		} catch (error) {
			console.error('Failed to connect to Redis:', error);
		}
	}

	private getPrefixedKey(key: string): string {
		return this.keyPrefix + key;
	}

	async set(key: string, value: unknown, ttlSeconds?: number): Promise<void> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			const serializedValue = JSON.stringify(value);

			if (ttlSeconds) {
				await this.client.setEx(prefixedKey, ttlSeconds, serializedValue);
			} else {
				await this.client.set(prefixedKey, serializedValue);
			}
		} catch (error) {
			console.error('Redis SET error:', error);
			throw error;
		}
	}

	async get<T>(key: string): Promise<T | null> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			const value = await this.client.get(prefixedKey);

			if (value === null) {
				return null;
			}

			try {
				return JSON.parse(value) as T;
			} catch {
				// If JSON parsing fails, return the raw value
				return value as unknown as T;
			}
		} catch (error) {
			console.error('Redis GET error:', error);
			throw error;
		}
	}

	async del(key: string): Promise<number> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			return await this.client.del(prefixedKey);
		} catch (error) {
			console.error('Redis DEL error:', error);
			throw error;
		}
	}

	async exists(key: string): Promise<boolean> {
		try {
			const prefixedKey = this.getPrefixedKey(key);
			const result = await this.client.exists(prefixedKey);
			return result === 1;
		} catch (error) {
			console.error('Redis EXISTS error:', error);
			throw error;
		}
	}

	async keys(pattern: string = '*'): Promise<string[]> {
		try {
			const prefixedPattern = this.getPrefixedKey(pattern);
			const keys = await this.client.keys(prefixedPattern);
			// Remove prefix from returned keys
			return keys.map(key => key.replace(this.keyPrefix, ''));
		} catch (error) {
			console.error('Redis KEYS error:', error);
			throw error;
		}
	}

	async flushAll(): Promise<void> {
		try {
			await this.client.flushAll();
		} catch (error) {
			console.error('Redis FLUSHALL error:', error);
			throw error;
		}
	}

	async ping(): Promise<string> {
		try {
			return await this.client.ping();
		} catch (error) {
			console.error('Redis PING error:', error);
			throw error;
		}
	}

	async onModuleDestroy(): Promise<void> {
		if (this.client) {
			await this.client.disconnect();
			console.log('Redis client disconnected');
		}
	}

	// Getter for direct client access if needed
	getClient(): RedisClientType {
		return this.client;
	}
}
