import type { PageDto } from '@common/dto/page.dto';
import { ResponseDto } from '@common/dto/response.dto.ts';
import { SocialCode, UserAccountType } from '@constants/user';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto.ts';
import { TransactionHistoryDto } from '@modules/payment/dtos/transaction-history.dto';
import type { TransactionHistoryOptionsDto } from '@modules/payment/dtos/transaction-history-options.dto';
import { PaymentTransactionEntity } from '@modules/payment/payment-transaction.entity';
import { UserAccountDto } from '@modules/user/dtos/user-account.dto';
import {
	BadRequestException,
	ConflictException,
	Injectable,
	NotFoundException,
} from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { ApiConfigService } from '@shared/services/api-config.service';
import { plainToClass } from 'class-transformer';
import { GeneratorProvider } from 'providers/generator.provider.ts';
import type { FindManyOptions, FindOptionsWhere } from 'typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import { UserNotFoundException } from '../../exceptions/user-not-found.exception.ts';
import { UserRegisterDto } from '../auth/dto/user-register.dto.ts';
import { UserRegisterSsoDto } from '../auth/dto/user-register-sso.dto.ts';
import { CreateUserProfileCommand } from './commands/create-user-profile.command.ts';
import { CreateUserProfileDto } from './dtos/create-user-profile.dto.ts';
import type { UpdateUserProfileDto } from './dtos/update-user-profile.dto.ts';
import type { UserQuickplayLinkDto } from './dtos/user-quickplay-link.dto.ts';
import type { UserQuickplayLoginDto } from './dtos/user-quickplay-login.dto.ts';
import { UserQuickplayResponseDto } from './dtos/user-quickplay-response.dto.ts';
import type { UsersPageOptionsDto } from './dtos/users-page-options.dto.ts';
import { UserProfileNotFoundException } from './exceptions/user-profile-not-found.exception.ts';
import { UserAccountEntity } from './user-account.entity.ts';
import { UserProfileEntity } from './user-profile.entity.ts';

@Injectable()
export class UserService {
	constructor(
		@InjectRepository(UserAccountEntity)
		private userRepository: Repository<UserAccountEntity>,
		@InjectRepository(UserProfileEntity)
		private userProfileRepository: Repository<UserProfileEntity>,
		@InjectRepository(PaymentTransactionEntity)
		private paymentTransactionRepository: Repository<PaymentTransactionEntity>,
		// private validatorService: ValidatorService,
		private commandBus: CommandBus,
		private configService: ApiConfigService,
	) {}

	findOne(
		findData: FindOptionsWhere<UserAccountEntity>,
	): Promise<UserAccountEntity | null> {
		return this.userRepository.findOneBy(findData);
	}

	find(
		findData: FindManyOptions<UserAccountEntity>,
	): Promise<UserAccountEntity[]> {
		return this.userRepository.find(findData);
	}

	findByUsernameOrEmail(
		options: Partial<{ username?: string | null; email?: string | null }>,
	): Promise<UserAccountEntity | null> {
		const queryBuilder = this.userRepository.createQueryBuilder('user');

		if (options.email) {
			queryBuilder.orWhere('user.email = :email', {
				email: options.email,
			});
		}

		if (options.username) {
			queryBuilder.orWhere('user.username = :username', {
				username: options.username,
			});
		}

		return queryBuilder.getOne();
	}

	@Transactional()
	async createUser(
		userRegisterDto: UserRegisterDto,
		ip: string,
	): Promise<UserAccountEntity> {
		const user = this.userRepository.create(userRegisterDto);

		// if (file && !this.validatorService.isImage(file.mimetype)) {
		//   throw new FileNotImageException();
		// }


		user.accountType = UserAccountType.LOCAL;
		user.passwordHash = userRegisterDto.password;
		user.createdAtIp = ip;

		await this.userRepository.save(user);

		user.userProfile = await this.createUserProfile(
			user.userId,
			plainToClass(CreateUserProfileDto, {
				displayName: null,
				gender: null,
				avatarUrl: null,
				dob: null,
				phone: null,
				address: null,
			}),
		);

		return user;
	}

	@Transactional()
	async createUserSSO(
		socialInfo: SocialInfoDto,
		ip: string,
	): Promise<UserAccountEntity> {
		const userRegisterSsoDto = new UserRegisterSsoDto({
			username: `${SocialCode.get(socialInfo.provider)}_${socialInfo.socialUid}`, // Use email as username for SSO users
			email: socialInfo.email || null, // SSO users may not have an email
			accountType: socialInfo.provider, // Use the provider as the account type
			socialUid: socialInfo.socialUid, // This can be set later based on the SSO provider
			linkedAt: new Date(),
			socialAccessToken: socialInfo.accessToken,
			socialRefreshToken: socialInfo.refreshToken,
			createdAtIp: ip,
		});

		const user = this.userRepository.create(userRegisterSsoDto);

		await this.userRepository.save(user);

		user.userProfile = await this.createUserProfile(
			user.userId,
			plainToClass(CreateUserProfileDto, {
				displayName: socialInfo.name,
				gender: null,
				avatarUrl: socialInfo.avatarUrl,
				dob: null,
				phone: null,
				address: null,
			}),
		);

		return user;
	}

	@Transactional()
	async linkUserSSO(
		userAccount: UserAccountEntity,
		socialInfo: SocialInfoDto,
	): Promise<UserAccountEntity> {
		userAccount.socialUid = socialInfo.socialUid;
		userAccount.accountType = socialInfo.provider;
		userAccount.linkedAt = new Date();
		userAccount.socialAccessToken = socialInfo.accessToken ?? null;
		userAccount.socialRefreshToken = socialInfo.refreshToken ?? null;

		return await this.userRepository.save(userAccount);
	}

	@Transactional()
	async createUserQuickplay(
		userQuickplayDto: UserQuickplayLoginDto,
		ip: string,
	): Promise<UserQuickplayResponseDto> {
		const username = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${GeneratorProvider.uuid()}`;

		const user = this.userRepository.create({
			username,
			email: null,
			accountType: UserAccountType.QUICKPLAY,
			createdAtIp: ip,
		});

		// Hash pseudo-password để tránh bị lộ
		const rawPassword = `${userQuickplayDto.platform}-${userQuickplayDto.uniqueId}-${userQuickplayDto.deviceId}`;
		user.passwordHash = rawPassword;

		const savedUser = await this.userRepository.save(user);

		// Tạo profile mặc định nếu chưa có (quickplay chưa cần)
		// await this.createUserProfile(
		//   savedUser.userId,
		//   plainToClass(CreateUserProfileDto, {
		//     displayName: null,
		//     gender: null,
		//     avatarUrl: null,
		//     dob: null,
		//     phone: null,
		//     address: null,
		//   }),
		// );

		return new UserQuickplayResponseDto({
			qpId: savedUser.username!,
			qpToken: userQuickplayDto.uniqueId,
			createdAt: savedUser.createdAt,
		});
	}

	async loginUserQuickplay(
		userQuickplayDto: UserQuickplayLoginDto,
		ip: string,
	): Promise<UserQuickplayResponseDto> {
		const quickplayUsername = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${userQuickplayDto.username}`;

		const user = await this.findOne({
			username: quickplayUsername,
			accountType: UserAccountType.QUICKPLAY,
		});

		if (!user) {
			throw new NotFoundException('Không tìm thấy người dùng Quickplay');
		}

		// Cập nhật thông tin đăng nhập (không cần await nếu không cần chặn)
		await this.userRepository.update(user.userId, {
			lastLoginAt: new Date(),
			lastLoginAtIp: ip,
		});

		return new UserQuickplayResponseDto({
			qpId: user.username!,
			qpToken: userQuickplayDto.uniqueId,
			createdAt: user.createdAt,
		});
	}

	@Transactional()
	async linkUserQuickplay(
		userQuickplayDto: UserQuickplayLinkDto,
		ip: string,
	): Promise<ResponseDto<null>> {
		const quickplayUsername = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${userQuickplayDto.qpId}`;

		const user = await this.userRepository.findOneBy({
			username: quickplayUsername,
			accountType: UserAccountType.QUICKPLAY,
		});

		if (!user) {
			throw new NotFoundException('Không tìm thấy người dùng Quickplay');
		}

		// Check nếu đã từng liên kết trước đó
		if (user.accountType !== UserAccountType.QUICKPLAY) {
			throw new BadRequestException('Tài khoản này đã được liên kết trước đó');
		}

		// Kiểm tra username mới có bị trùng không
		const existing = await this.userRepository.findOneBy({
			username: userQuickplayDto.username!,
		});

		if (existing) {
			throw new ConflictException('Username đã tồn tại');
		}

		// Cập nhật thông tin
		user.lastLoginAt = new Date();
		user.lastLoginAtIp = ip;
		user.username = userQuickplayDto.username;
		user.passwordHash = userQuickplayDto.password;
		user.accountType = UserAccountType.LOCAL;

		await this.userRepository.save(user);

		// Tạo user profile nếu chưa có
		if (!user.userProfile) {
			user.userProfile = await this.createUserProfile(
				user.userId,
				plainToClass(CreateUserProfileDto, {
					displayName: null,
					gender: null,
					avatarUrl: null,
					dob: null,
					phone: null,
					address: null,
				}),
			);
		}

		return new ResponseDto<null>({
			success: true,
			message: 'Người dùng Quickplay đã liên kết thành công',
		});
	}

	async getSocialInfo(user: UserAccountEntity): Promise<SocialInfoDto> {
		const isSSOAccount =
			user.accountType === UserAccountType.FACEBOOK ||
			user.accountType === UserAccountType.GOOGLE ||
			user.accountType === UserAccountType.APPLE;

		if (!isSSOAccount) {
			throw new BadRequestException(
				'Tài khoản này không phải là tài khoản đăng nhập mạng xã hội.',
			);
		}

		if (!user.socialUid) {
			throw new NotFoundException(
				'Không tìm thấy UID mạng xã hội cho người dùng này.',
			);
		}

		return new SocialInfoDto({
			socialUid: user.socialUid,
			name: user.userProfile?.displayName || null,
			email: user.email || null,
			avatarUrl: user.userProfile?.avatarUrl || null,
			provider: user.accountType as UserAccountType,
		});
	}

	@Transactional()
	async unlinkSocialAccount(
		user: UserAccountEntity,
		provider: string,
	): Promise<ResponseDto<null>> {
		const validProviders = [
			UserAccountType.FACEBOOK,
			UserAccountType.GOOGLE,
			UserAccountType.APPLE,
		];

		// Validate: is SSO account type?
		if (!validProviders.includes(user.accountType as UserAccountType)) {
			throw new BadRequestException(
				'Tài khoản này không được liên kết với thông tin đăng nhập mạng xã hội.',
			);
		}

		// Validate: correct provider?
		if (user.accountType !== provider) {
			throw new ConflictException(
				`Tài khoản này không được liên kết với ${provider}.`,
			);
		}

		// Validate: has social UID?
		if (!user.socialUid) {
			throw new NotFoundException(
				'Hiện không có tài khoản xã hội nào được liên kết.',
			);
		}

		// Proceed to unlink
		user.socialUid = null;
		user.socialAccessToken = null;
		user.socialRefreshToken = null;
		user.linkedAt = null;

		// Downgrade account type to LOCAL
		user.accountType = UserAccountType.LOCAL;

		await this.userRepository.save(user);

		return new ResponseDto<null>({
			success: true,
			message: 'Tài khoản xã hội đã được hủy liên kết thành công',
		});
	}

	async getUsers(
		pageOptionsDto: UsersPageOptionsDto,
	): Promise<PageDto<UserAccountDto>> {
		const queryBuilder = this.userRepository.createQueryBuilder('user');
		const [items, pageMetaDto] = await queryBuilder.paginate(pageOptionsDto);

		return items.toPageDto(pageMetaDto);
	}

	async getUser(userId: number): Promise<UserAccountDto> {
		const queryBuilder = this.userRepository.createQueryBuilder('user');

		queryBuilder.where('user.id = :userId', { userId });

		const userAccountEntity = await queryBuilder.getOne();

		if (!userAccountEntity) {
			throw new UserNotFoundException();
		}

		return userAccountEntity.toDto();
	}

	createUserProfile(
		userId: number,
		createUserProfileDto: CreateUserProfileDto,
	): Promise<UserProfileEntity> {
		return this.commandBus.execute<CreateUserProfileCommand, UserProfileEntity>(
			new CreateUserProfileCommand(userId, createUserProfileDto),
		);
	}

	updateLastLoginInfo(userId: number, ip: string): Promise<UserAccountEntity> {
		return this.userRepository.save({
			userId,
			lastLoginAt: new Date(),
			lastLoginAtIp: ip,
		});
	}

	updateRefreshToken(
		userId: number,
		refreshToken?: string | null,
	): Promise<UserAccountEntity> {
		const refreshTokenExpiresAt = refreshToken
			? new Date(
					Date.now() +
						this.configService.authConfig.jwtRefreshTokenExpirationTime * 10,
				)
			: new Date(Date.now());

		return this.userRepository.save({
			userId,
			refreshToken,
			refreshTokenExpiresAt,
		});
	}

	async updateUserProfile(
		user: UserAccountEntity,
		updateUserProfileDto: UpdateUserProfileDto,
	): Promise<ResponseDto<null>> {
		const userProfileEntity = await this.userProfileRepository.findOneBy({
			userId: user.userId,
		});

		if (!userProfileEntity) {
			throw new UserProfileNotFoundException();
		}

		this.userProfileRepository.merge(userProfileEntity, updateUserProfileDto);
		await this.userProfileRepository.save(userProfileEntity);

		return new ResponseDto<null>({
			success: true,
			message: 'Hồ sơ người dùng đã được cập nhật thành công',
		});
	}

	async getTransactionHistory(
		user: UserAccountEntity,
		transactionHistoryOptionsDto: TransactionHistoryOptionsDto,
	): Promise<PageDto<TransactionHistoryDto>> {
		// Debug logging to see what values are received
		console.info('Filter values received:', {
			filterStatus: transactionHistoryOptionsDto.filterStatus,
			dateFrom: transactionHistoryOptionsDto.dateFrom,
			dateTo: transactionHistoryOptionsDto.dateTo,
		});

		const queryBuilder = this.paymentTransactionRepository
			.createQueryBuilder('payment_transaction')
			.leftJoin('payment_transaction.user', 'user')
			.where('user.userId = :userId', { userId: user.userId })
			.orderBy('payment_transaction.createdAt', 'DESC');

		// Apply status filter if provided
		if (transactionHistoryOptionsDto.filterStatus) {
			console.info(
				'Applying status filter:',
				transactionHistoryOptionsDto.filterStatus,
			);
			queryBuilder.andWhere('payment_transaction.status = :status', {
				status: transactionHistoryOptionsDto.filterStatus,
			});
		}

		// Apply date range filter if provided
		if (transactionHistoryOptionsDto.dateFrom) {
			console.info(
				'Applying dateFrom filter:',
				transactionHistoryOptionsDto.dateFrom,
			);
			queryBuilder.andWhere('payment_transaction.createdAt >= :dateFrom', {
				dateFrom: transactionHistoryOptionsDto.dateFrom,
			});
		}

		if (transactionHistoryOptionsDto.dateTo) {
			console.info(
				'Applying dateTo filter:',
				transactionHistoryOptionsDto.dateTo,
			);
			queryBuilder.andWhere('payment_transaction.createdAt <= :dateTo', {
				dateTo: transactionHistoryOptionsDto.dateTo,
			});
		}

		// Log the final SQL query
		console.info('Final SQL query:', queryBuilder.getSql());
		console.info('Query parameters:', queryBuilder.getParameters());

		const [items, pageMetaDto] = await queryBuilder.paginate(
			transactionHistoryOptionsDto,
		);

		return items.toPageDto(pageMetaDto, TransactionHistoryDto);
	}
}
