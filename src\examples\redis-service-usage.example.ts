import { Injectable } from '@nestjs/common';
import { RedisService } from '@shared/services/redis.service';

/**
 * Example service showing how to use RedisService in your application
 */
@Injectable()
export class ExampleService {
	constructor(private readonly redisService: RedisService) {}

	// Example: Cache user session data
	async cacheUserSession(userId: string, sessionData: any): Promise<void> {
		const key = `user_session:${userId}`;
		const ttl = 3600; // 1 hour
		await this.redisService.set(key, sessionData, ttl);
	}

	async getUserSession(userId: string): Promise<any | null> {
		const key = `user_session:${userId}`;
		return await this.redisService.get(key);
	}

	async clearUserSession(userId: string): Promise<boolean> {
		const key = `user_session:${userId}`;
		const deleted = await this.redisService.del(key);
		return deleted > 0;
	}

	// Example: Cache API responses
	async cacheApiResponse(endpoint: string, data: any, ttlSeconds: number = 300): Promise<void> {
		const key = `api_cache:${endpoint}`;
		await this.redisService.set(key, data, ttlSeconds);
	}

	async getCachedApiResponse<T>(endpoint: string): Promise<T | null> {
		const key = `api_cache:${endpoint}`;
		return await this.redisService.get<T>(key);
	}

	// Example: Rate limiting
	async checkRateLimit(userId: string, maxRequests: number = 100, windowSeconds: number = 3600): Promise<boolean> {
		const key = `rate_limit:${userId}`;
		const current = await this.redisService.get<number>(key);
		
		if (current === null) {
			// First request in window
			await this.redisService.set(key, 1, windowSeconds);
			return true;
		}
		
		if (current >= maxRequests) {
			return false; // Rate limit exceeded
		}
		
		// Increment counter
		await this.redisService.set(key, current + 1, windowSeconds);
		return true;
	}

	// Example: Distributed locks
	async acquireLock(lockKey: string, ttlSeconds: number = 30): Promise<boolean> {
		const key = `lock:${lockKey}`;
		const exists = await this.redisService.exists(key);
		
		if (exists) {
			return false; // Lock already exists
		}
		
		await this.redisService.set(key, Date.now(), ttlSeconds);
		return true;
	}

	async releaseLock(lockKey: string): Promise<void> {
		const key = `lock:${lockKey}`;
		await this.redisService.del(key);
	}

	// Example: Pub/Sub pattern (using direct client access)
	async publishMessage(channel: string, message: any): Promise<void> {
		const client = this.redisService.getClient();
		await client.publish(channel, JSON.stringify(message));
	}

	// Example: Game state caching
	async cacheGameState(gameId: string, gameState: any): Promise<void> {
		const key = `game_state:${gameId}`;
		const ttl = 7200; // 2 hours
		await this.redisService.set(key, gameState, ttl);
	}

	async getGameState(gameId: string): Promise<any | null> {
		const key = `game_state:${gameId}`;
		return await this.redisService.get(key);
	}

	// Example: Leaderboard caching
	async cacheLeaderboard(leaderboardType: string, data: any[]): Promise<void> {
		const key = `leaderboard:${leaderboardType}`;
		const ttl = 300; // 5 minutes
		await this.redisService.set(key, data, ttl);
	}

	async getLeaderboard(leaderboardType: string): Promise<any[] | null> {
		const key = `leaderboard:${leaderboardType}`;
		return await this.redisService.get<any[]>(key);
	}

	// Example: Configuration caching
	async cacheConfig(configKey: string, configValue: any): Promise<void> {
		const key = `config:${configKey}`;
		const ttl = 1800; // 30 minutes
		await this.redisService.set(key, configValue, ttl);
	}

	async getConfig<T>(configKey: string): Promise<T | null> {
		const key = `config:${configKey}`;
		return await this.redisService.get<T>(key);
	}

	// Example: Cleanup operations
	async cleanupExpiredSessions(): Promise<number> {
		const keys = await this.redisService.keys('user_session:*');
		let cleaned = 0;
		
		for (const key of keys) {
			const exists = await this.redisService.exists(key);
			if (!exists) {
				cleaned++;
			}
		}
		
		return cleaned;
	}

	// Example: Health check
	async isRedisHealthy(): Promise<boolean> {
		try {
			const result = await this.redisService.ping();
			return result === 'PONG';
		} catch {
			return false;
		}
	}
}
