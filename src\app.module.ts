import path from 'node:path';

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClsModule } from 'nestjs-cls';
import {
	AcceptLanguageResolver,
	HeaderResolver,
	I18nModule,
	QueryResolver,
} from 'nestjs-i18n';
import { DataSource } from 'typeorm';
import { addTransactionalDataSource } from 'typeorm-transactional';

import { AuthModule } from './modules/auth/auth.module.ts';
import { HealthCheckerModule } from './modules/health-checker/health-checker.module.ts';
import { PostModule } from './modules/post/post.module.ts';
import { UserModule } from './modules/user/user.module.ts';
import { ApiConfigService } from './shared/services/api-config.service.ts';
import { SharedModule } from './shared/shared.module.ts';
import { CacheModule } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-store';

@Module({
	imports: [
		CacheModule.registerAsync({
			isGlobal: true,
			useFactory: async (configService: ApiConfigService) => ({
				store: await redisStore(configService.redisConfig.socket),
				ttl: configService.redisConfig.ttl,
			}),
			inject: [ApiConfigService],
		}),
		AuthModule,
		UserModule,
		PostModule,
		ClsModule.forRoot({
			global: true,
			middleware: {
				mount: true,
			},
		}),
		ThrottlerModule.forRootAsync({
			imports: [SharedModule],
			useFactory: (configService: ApiConfigService) => ({
				throttlers: [configService.throttlerConfigs],
			}),
			inject: [ApiConfigService],
		}),
		ConfigModule.forRoot({
			isGlobal: true,
			envFilePath: '.env',
		}),
		TypeOrmModule.forRootAsync({
			imports: [SharedModule],
			useFactory: (configService: ApiConfigService) =>
				configService.postgresConfig,
			inject: [ApiConfigService],
			dataSourceFactory: (options) => {
				if (!options) {
					throw new Error('Invalid options passed');
				}

				return Promise.resolve(
					addTransactionalDataSource(new DataSource(options)),
				);
			},
		}),
		// eslint-disable-next-line canonical/id-match
		I18nModule.forRootAsync({
			useFactory: (configService: ApiConfigService) => ({
				fallbackLanguage: configService.fallbackLanguage,
				loaderOptions: {
					// eslint-disable-next-line n/no-unsupported-features/node-builtins
					path: path.join(import.meta.dirname, 'i18n/'),
					watch: configService.isDevelopment,
				},
			}),
			resolvers: [
				{ use: QueryResolver, options: ['lang'] },
				AcceptLanguageResolver,
				new HeaderResolver(['x-lang']),
			],
			imports: [SharedModule],
			inject: [ApiConfigService],
		}),
		HealthCheckerModule,
	],
	providers: [],
})
export class AppModule {}
